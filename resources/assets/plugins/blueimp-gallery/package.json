{"name": "blueimp-gallery", "version": "3.4.0", "title": "blueimp Gallery", "description": "blueimp Gallery is a touch-enabled, responsive and customizable image and video gallery, carousel and lightbox, optimized for both mobile and desktop web browsers. It features swipe, mouse and keyboard navigation, transition effects, slideshow functionality, fullscreen support and on-demand content loading and can be extended to display additional content types.", "keywords": ["image", "video", "gallery", "carousel", "lightbox", "mobile", "desktop", "touch", "responsive", "swipe", "mouse", "keyboard", "navigation", "transition", "effects", "slideshow", "fullscreen"], "homepage": "https://github.com/blueimp/Gallery", "author": {"name": "<PERSON>", "url": "https://blueimp.net"}, "repository": {"type": "git", "url": "git://github.com/blueimp/Gallery.git"}, "license": "MIT", "devDependencies": {"clean-css-cli": "5", "eslint": "7", "eslint-config-blueimp": "2", "eslint-config-prettier": "8", "eslint-plugin-jsdoc": "36", "eslint-plugin-prettier": "4", "jquery": "1", "prettier": "2", "stylelint": "13", "stylelint-config-prettier": "8", "stylelint-config-recommended": "5", "uglify-js": "3"}, "stylelint": {"extends": ["stylelint-config-recommended", "stylelint-config-prettier"], "ignoreFiles": ["css/*.min.css"]}, "eslintConfig": {"extends": ["blueimp", "plugin:jsdoc/recommended", "plugin:prettier/recommended"], "env": {"browser": true}}, "eslintIgnore": ["js/*.min.js", "js/vendor"], "prettier": {"arrowParens": "avoid", "proseWrap": "always", "semi": false, "singleQuote": true, "trailingComma": "none"}, "scripts": {"test": "stylelint '**/*.css' && eslint .", "prebuild": "bin/sync-vendor-libs.sh", "build:js": "cd js && uglifyjs blueimp-helper.js blueimp-gallery.js blueimp-gallery-fullscreen.js blueimp-gallery-indicator.js blueimp-gallery-video.js blueimp-gallery-vimeo.js blueimp-gallery-youtube.js --ie8 -c -m -o blueimp-gallery.min.js --source-map url=blueimp-gallery.min.js.map", "build:jquery": "cd js && uglifyjs blueimp-gallery.js blueimp-gallery-fullscreen.js blueimp-gallery-indicator.js blueimp-gallery-video.js blueimp-gallery-vimeo.js blueimp-gallery-youtube.js jquery.blueimp-gallery.js --ie8 -c -m -o jquery.blueimp-gallery.min.js --source-map url=jquery.blueimp-gallery.min.js.map", "build:css": "cd css && cleancss -c ie7 --source-map -o blueimp-gallery.min.css blueimp-gallery.css blueimp-gallery-indicator.css blueimp-gallery-video.css", "build": "npm run build:js && npm run build:jquery && npm run build:css", "preversion": "npm test", "version": "npm run build && git add -A js css", "postversion": "git push --tags origin master master:gh-pages && npm publish"}, "files": ["css/*.css", "css/*.css.map", "img/*.gif", "img/*.png", "img/*.svg", "js/*.js", "js/*.js.map"], "main": "js/blueimp-gallery.js"}