<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/general/preparar.php';

$param                 = array();
$matchup               = '';
$fecha                 = create_date();
$torneo_filter         = '';
$solo_odds_por_revisar = 0;
$solo_por_jugar        = 0;
$tabselected           = 1;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'El partido ha sido ingresado.';
        }
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'El partido ha sido modificado.';
        }
        if (isset($_GET['nnpp'])) {
            $success_display = 'show';
            $success_text = 'No hay mas partidos por revisarle probabilidades.';
        }
        if (isset($_SESSION['matchup'])) {
            $matchup = $_SESSION['matchup'];
        
            unset($_SESSION['matchup']);
        }
        if (isset($_SESSION['fecha_lpartidos'])) {
            $fecha = $_SESSION['fecha_lpartidos'];
        }
        if (isset($_SESSION['solo_por_jugar_lpartidos'])) {
            $solo_por_jugar = $_SESSION['solo_por_jugar_lpartidos'];
        }
        if (isset($_SESSION['solo_odds_por_revisar_lpartidos'])) {
            $solo_odds_por_revisar = $_SESSION['solo_odds_por_revisar_lpartidos'];
        }
        if (isset($_SESSION['torneo_filter_lpartidos'])) {
            $torneo_filter = $_SESSION['torneo_filter_lpartidos'];
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $matchup               = limpiar_datos($_POST['matchup']);
        $fecha                 = limpiar_datos($_POST['fecha']);
        $torneo_filter         = limpiar_datos($_POST['torneo']);
        $solo_odds_por_revisar = @getvalue_checkbox($_POST['solo_odds_por_revisar']);
        $solo_por_jugar        = @getvalue_checkbox($_POST['solo_por_jugar']);
        $tabselected           = limpiar_datos($_POST["tabselected"]);

        $_SESSION['matchup']                         = $matchup;
        $_SESSION['fecha_lpartidos']                 = $fecha;
        $_SESSION['torneo_filter_lpartidos']         = $torneo_filter;
        $_SESSION['solo_por_jugar_lpartidos']        = $solo_por_jugar;
        $_SESSION['solo_odds_por_revisar_lpartidos'] = $solo_odds_por_revisar;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_editpartido
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editpartido'])) {
    try {
        $method_editpartido = 1;

        $_SESSION['idpartido'] = limpiar_datos($_POST['mdl_review_partido_idpartido']);

        header('Location: epartido');
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_editpartido
#region sub_verinfo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verinfo'])) {
    try {
        $_SESSION['idpartido'] = limpiar_datos($_POST['mdl_review_partido_idpartido']);

        header('Location: vpartidoinfo');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verinfo
#region sub_delpartido
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delpartido'])) {
    try {
        $method_sub_delpartido = 1;
        
        $mdlidpartido = limpiar_datos($_POST['mdl_review_partido_idpartido']);

        Partido::delete($mdlidpartido, $conexion);

        $success_display = 'show';
        $success_text = 'El partido ha sido eliminado.';

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delpartido
#region sub_verodds
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_verodds'])) {
    try {
        $_SESSION['idpartido'] = limpiar_datos($_POST['mdl_review_partido_idpartido']);

        header('Location: ipartido_odds');
        exit();

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_verodds
#region sub_ver_probabilidades
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_ver_probabilidades'])) {
	try {
		$_SESSION['id_partido'] = limpiar_datos($_POST['mdl_review_partido_idpartido']);
		
		header('Location: epartido_probabilidades');
		exit();
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_ver_probabilidades
#region sub_eliminar_partidosporrevisar_hasta
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_eliminar_partidosporrevisar_hasta'])) {
	try {
		$hora_hasta = limpiar_datos($_POST['eliminar_partidosporrevisar_hasta_hora']);
		
		Partido::delete_hasta_hora($fecha, $hora_hasta, $conexion);
		
		$success_display = 'show';
		$success_text    = 'Partidos eliminados.';
		
	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion sub_eliminar_partidosporrevisar_hasta
#region try
try {
    $method_try = 1;

    $param['matchup']               = $matchup;
    $param['fecha']                 = $fecha;
    $param['torneo']                = $torneo_filter;
    $param['solo_odds_por_revisar'] = $solo_odds_por_revisar;
    $param['solo_por_jugar']        = $solo_por_jugar;
    $partidos                       = Partido::getList($param, $conexion);

    $param['groupby_torneo'] = 1;
    $torneos                 = Partido::getList($param,$conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lpartidos.view.php';

?>