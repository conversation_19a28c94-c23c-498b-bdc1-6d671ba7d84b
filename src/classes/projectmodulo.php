<?php

require_once __ROOT__ . '/src/classes/project.php';

class ProjectModulo
{
	public string $id;
	public Project $project;
	public string $nombre;
	public string $descripcion;
	public int $estado;
	private string $bd_table       = 'projects_modulos';
	private string $bd_alias       = 'promod';
	private string $bd_id          = 'id';
	private string $bd_id_project  = 'id_project';
	private string $bd_nombre      = 'nombre';
	private string $bd_descripcion = 'descripcion';
	private string $bd_estado      = 'estado';
	
	function __construct()
	{
		$this->id          = '';
		$this->project     = new Project();
		$this->nombre      = '';
		$this->descripcion = '';
		$this->estado      = 0;
	}
	
	/**
	 * @param $resultado
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto                      = new self;
			$objeto->id                  = desordena($resultado[$cq->bd_id]);
			$objeto->project->id_project = desordena($resultado[$cq->bd_id_project]);
			$objeto->nombre              = $resultado[$cq->bd_nombre];
			$objeto->descripcion         = $resultado[$cq->bd_descripcion];
			$objeto->estado              = $resultado[$cq->bd_estado];
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, PDO $conexion): self
	{
		try {
			$cq = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list(array $paramref, PDO $conexion): array
	{
		try {
			$id_project = (isset($paramref['id_project'])) ? $paramref['id_project'] : "";
			
			$cq = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = 1 ";
			
			if(!empty($id_project)){
				$query .= "AND $cqa.$cq->bd_id_project = :$cq->bd_id_project ";
			}
			
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_nombre ";
			
			$statement = $conexion->prepare($query);
			
			if(!empty($id_project)){
				$statement->bindValue(":$cq->bd_id_project", ordena($id_project));
			}
			
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function agregar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			
			$cq = new self;
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "   $cq->bd_id_project ";
			$query .= "  ,$cq->bd_nombre ";
			$query .= "  ,$cq->bd_descripcion ";
			$query .= ") VALUES (";
			$query .= "   :$cq->bd_id_project ";
			$query .= "  ,:$cq->bd_nombre ";
			$query .= "  ,:$cq->bd_descripcion ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id_project", ordena($this->project->id_project));
			$statement->bindValue(":$cq->bd_nombre", $this->nombre);
			$statement->bindValue(":$cq->bd_descripcion", $this->descripcion);
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function modificar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "   $cq->bd_id_project = :$cq->bd_id_project ";
			$query .= "  ,$cq->bd_nombre = :$cq->bd_nombre ";
			$query .= "  ,$cq->bd_descripcion = :$cq->bd_descripcion ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id_project", ordena($this->project->id_project));
			$statement->bindValue(":$cq->bd_nombre", $this->nombre);
			$statement->bindValue(":$cq->bd_descripcion", $this->descripcion);
			$statement->bindValue(":$cq->bd_id", ordena($this->id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function eliminar($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data(): void
	{
		try {
			validar_campovacio($this->project->id_project, 'Debe especificar el proyecto');
			validar_campovacio($this->nombre, 'Debe especificar el nombre');
		
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>